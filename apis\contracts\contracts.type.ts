export interface SearchContract {
    Name?: string;
    ContractNumber?: string;
    UserNameCreated?: string;
    FromDate?: string;
    ToDate?: string;
    IsFinal?: boolean;
    Page?: number;
    PageSize?: number;
    IsDeleted?: boolean;
}
export interface ResponseContract {
    id: string;
    name: string;
    dealId: string;
    dealName: string;
    buyerCompanyName: string;
    quotationId: string;
    quotationName: string | null;
    buyerRepresentative: string;
    sellerStaffName: string;
    numbersVersion: number;
    updatedDateTime: string | null;
    createdDateTime: string;
    userNameCreated: string;
    contractNumber: string;
    signDate: string;
    isFinal: boolean;
    status: string;
    latestDateTime: string;
}
export interface IContract {
    id: string;
    name: string;
    version: string;
    dealId: string;
    quotationId: string;
    contractNumber: string;
    signDate: string;
    isFinal: boolean;
    buyer: {
        customerId: string;
        customerName: string;
        representative: string;
        position: string;
    };
    seller: {
        bankName: string;
        bankBranch: string;
        bankAccount: string;
        staffId: string;
        position: string;
    };
    payments: {
        paymentType: number;
        valuePercent: number;
        daysAfterSign: number;
        paymentDocumentType: number;
        paymentDocumentCount: number;
    }[];
    delivery: {
        deliveryType: number;
        deliveryWeek: number;
        regionDeliveryType: number;
        country: string;
        city: string;
        district: string;
        ward: string;
        addressDetail: string;
        documentIncludedType: number;
        documentQuantity: number;
    };
}
export interface StatusContract {
    data: string;
    isError: boolean;
    errorMessage: string;
    status: number;
}
export interface IContractShorten {
    id: string;
    name: string;
    createdDate: string;
    creatorName: string;
}
export interface ContractNumber {
    id: string;
    versionNumber: string;
    updateTime: string;
    updatedBy: string;
}
