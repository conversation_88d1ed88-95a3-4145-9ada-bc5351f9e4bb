'use client';
import { IContract } from '@/apis/contracts/contracts.type';
import { FormProvider, useForm } from 'react-hook-form';
import { <PERSON>, Button } from 'reactstrap';
import { useEffect, useState } from 'react';
import InfoGeneral from './InfoGeneral';
import InfoContract from './InfoContract';
import Buyer from './Buyer';
import Seller from './Seller';
import Payment from './Payment';
import Delivery from './Delivery';
import { generateWordPreview } from '@/preview/wordGenerator';
import ModalUpdate from '../../Modal/ModalUpdate';
import { useGetDetailCustomer } from '@/apis/customer/customer.api';

interface FormCreateAsicProps {
    onSubmit?: (data: IContract) => void;
    onClose: () => void;
    initValue?: IContract;
    onReplace?: (data: IContract) => void;
    onNewVersion?: (data: IContract) => void;
}
const FormCreateAsic = ({
    onSubmit,
    onClose,
    initValue,
    onReplace,
    onNewVersion,
}: FormCreateAsicProps) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const toggle = () => setIsModalOpen((prev) => !prev);
    const methods = useForm<IContract>({
        defaultValues: initValue || {
            delivery: {
                country: 'VN',
            },
        },
    });

    useEffect(() => {
        if (initValue) {
            methods.reset(initValue);
        } else {
            methods.reset({
                delivery: {
                    country: 'VN',
                },
            });
        }
    }, [initValue, methods]);

    const handleFormSubmit = (data: IContract) => {
        onSubmit?.(data);
    };

    const handlePreviewWord = () => {
        const currentData = methods.getValues();
        const dataCustomer = useGetDetailCustomer
        generateWordPreview(currentData);
    };

    const handleReplace = () => {
        const currentData = methods.getValues();
        onReplace?.(currentData);
    };

    const handleNewVersion = () => {
        const currentData = methods.getValues();
        onNewVersion?.(currentData);
    };
    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                <InfoGeneral />
                <InfoContract />
                <Buyer />
                <Seller />
                <Payment />
                <Delivery />
                <div
                    className='d-flex justify-content-between align-items-center mt-4'
                    style={{ padding: '20px 40px 20px 40px' }}
                >
                    <div>
                        <Button
                            type='button'
                            style={{
                                borderColor: '#f18069',
                                color: '#f18069',
                                backgroundColor: '#ffffff',
                            }}
                            onClick={onClose}
                        >
                            Hủy
                        </Button>
                    </div>
                    <div>
                        <Button
                            color='info'
                            type='button'
                            className='me-2'
                            style={{
                                borderColor: '#0ab39c',
                                color: '#0ab39c',
                                backgroundColor: '#ffffff',
                            }}
                            onClick={handlePreviewWord}
                        >
                            Xem trước
                        </Button>

                        {initValue ? (
                            <Button
                                color='primary'
                                type='submit'
                                style={{
                                    borderColor: '#ffffff',
                                    color: '#ffffff',
                                    backgroundColor: '#0ab39c',
                                }}
                                onClick={() => setIsModalOpen(true)}
                            >
                                Lưu
                            </Button>
                        ) : (
                            <Button
                                color='primary'
                                type='submit'
                                style={{
                                    borderColor: '#ffffff',
                                    color: '#ffffff',
                                    backgroundColor: '#0ab39c',
                                }}
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Tạo mới
                            </Button>
                        )}
                    </div>
                </div>
            </Card>
            <ModalUpdate
                isOpen={isModalOpen}
                toggle={toggle}
                onReplace={handleReplace}
                onNewVersion={handleNewVersion}
            />
        </FormProvider>
    );
};
export default FormCreateAsic;
